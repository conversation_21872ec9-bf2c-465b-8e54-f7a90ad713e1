# MCP Browser-Use 配置与故障排查手册

## 📋 概述

本手册记录了在 macOS 环境下配置 browser-use MCP 服务器时遇到的问题、原因分析及解决方案，用于快速排查和解决配置问题。

## 🖥️ 本机环境信息

### 系统环境
- **操作系统**: macOS (Apple Silicon)
- **默认终端**: zsh (`/bin/zsh`)
- **VS Code 终端**: zsh (与系统默认一致)
- **包管理器**: Homebrew
- **Python 环境**: 支持多版本 (pyenv)

### 已安装组件
- **UV 包管理器**:
  - 版本: `uvx 0.7.19`
  - 位置: `/opt/homebrew/bin/uvx`
  - 安装方式: Homebrew

- **MCP Server Browser-Use**:
  - 安装位置: `/Users/<USER>/.local/bin/mcp-server-browser-use`
  - 实际路径: `/Users/<USER>/.local/share/uv/tools/mcp-server-browser-use/bin/mcp-server-browser-use`
  - 依赖包数量: 166个包
  - 版本: 0.1.9 (包含 browser-use 0.1.41)

- **Playwright 浏览器**: 已安装 (Chromium, Firefox, Webkit, FFMPEG)

### PATH 环境变量
```zsh
/opt/homebrew/bin:/usr/local/lib/ruby/gems/3.2.0/bin:/usr/local/opt/ruby/bin:
/Users/<USER>/.pyenv/shims:/Users/<USER>/.pyenv/bin:/usr/local/bin:...
```
**注意**: `/Users/<USER>/.local/bin` 之前不在系统 PATH 中，后面已经修改

## 🔍 问题分析：为什么两种配置一个成功一个失败？

### ❌ 失败的配置 (uvx 方式)
```json
{
  "command": "uvx",
  "args": ["mcp-server-browser-use@latest"]
}
```

### ✅ 成功的配置 (直接路径方式)
```json
{
  "command": "/Users/<USER>/.local/bin/mcp-server-browser-use",
  "args": []
}
```

### 🔬 失败原因深度分析

#### 1. **启动时间差异**
- **uvx 方式**: 需要1.38秒解析和安装166个依赖包
- **直接路径**: 立即启动，无依赖解析过程

#### 2. **MCP 协议超时机制** 
- MCP 客户端期望服务器快速响应握手
- uvx 的依赖解析时间可能超出 Augment Code 的超时限制
- 直接路径避免了这个问题

#### 3. **环境隔离差异**
- **uvx**: 每次创建临时虚拟环境，环境可能不一致
- **直接路径**: 使用固定的预安装环境，稳定可靠

#### 4. **进程启动流程**
```
uvx 方式:
Augment Code → uvx → 解析依赖 → 创建环境 → 启动服务器
                ↑ 可能在这里超时

直接路径方式:
Augment Code → 直接启动服务器
                ↑ 立即响应
```

## 🎯 配置方式选择策略

### 📋 选择判断标准

#### ✅ **使用 uvx 相对路径的情况（标准做法）**
```json
{
  "command": "uvx",
  "args": ["package-name@latest"]
}
```

**适用场景**：
- **轻量级 MCP 服务器**：依赖包少（<50个），启动时间快（<0.5秒）
- **官方或成熟的 MCP**：如 `mcp-server-filesystem`、`mcp-server-git` 等
- **开发和测试环境**：需要频繁更新版本，希望自动获取最新功能

#### ⚠️ **使用直接路径的情况（特殊情况）**
```json
{
  "command": "/path/to/mcp-server",
  "args": []
}
```

**适用场景**：
- **重量级 MCP 服务器**：依赖包多（>100个），启动时间长（>1秒）
- **对启动速度敏感的场景**：生产环境，需要快速响应
- **uvx 环境问题**：PATH 配置问题、权限限制、网络环境限制

### 🔍 **browser-use MCP 的特殊性**

**为什么需要特殊处理？**
1. **依赖复杂度高**：166个 Python 包 + Playwright 浏览器引擎
2. **启动时间长**：uvx 启动 ≈ 1.4秒 vs 普通 MCP < 0.5秒
3. **环境要求严格**：需要浏览器二进制文件、特定 Python 环境

### � **推荐的选择流程**

1. **默认尝试 uvx 相对路径**
2. **测试启动时间**：`time uvx mcp-server-xxx@latest --help`
3. **如果 > 1秒或遇到问题，切换到直接路径**

## �🔧 推荐配置

### ✅ browser-use 稳定配置（推荐）

```json
{
  "mcpServers": {
    "browser-use": {
      "command": "/Users/<USER>/.local/bin/mcp-server-browser-use",
      "args": [],
      "env": {
        "MCP_LLM_GOOGLE_API_KEY": "your-google-api-key",
        "MCP_LLM_PROVIDER": "google",
        "MCP_LLM_MODEL_NAME": "gemini-2.0-flash-exp",
        "MCP_BROWSER_HEADLESS": "true"
      }
    }
  }
}
```

### 🔄 其他 MCP 标准配置示例

#### 轻量级 MCP（推荐 uvx）
```json
{
  "mcpServers": {
    "filesystem": {
      "command": "uvx",
      "args": ["mcp-server-filesystem@latest"]
    },
    "git": {
      "command": "uvx",
      "args": ["mcp-server-git@latest"]
    }
  }
}
```

#### 混合配置（生产环境推荐）
```json
{
  "mcpServers": {
    "lightweight-mcp": {
      "command": "uvx",
      "args": ["mcp-server-filesystem@latest"]
    },
    "heavy-mcp": {
      "command": "/Users/<USER>/.local/bin/mcp-server-browser-use",
      "args": []
    }
  }
}
```

## 🚨 常见问题与解决方案

### 问题 1: uvx 配置失败但直接路径成功

**现象**：
- 使用 `"command": "uvx"` 配置失败
- 使用直接路径 `"/Users/<USER>/.local/bin/mcp-server-browser-use"` 成功

**根本原因**：
基于上述环境分析，失败的根本原因是：
1. **启动超时**: uvx 需要1.38秒解析166个依赖包，超出 MCP 握手超时限制
2. **环境不稳定**: 动态创建的临时环境不如预安装的固定环境稳定
3. **PATH 依赖**: Augment Code 运行环境可能与终端环境的 PATH 不同

**解决方案**：
1. **推荐**：使用直接路径配置（已验证稳定）
2. **备选**：使用完整路径的 uvx：`/opt/homebrew/bin/uvx`
3. **不推荐**：修改系统 PATH（影响范围太大）

### 问题 2: 工具调用失败

**现象**：
- `run_browser_agent` 返回空结果
- `run_deep_research` 报错

**排查步骤**：
1. 检查 API 密钥是否有效
2. 验证网络连接
3. 查看 Augment Code 日志
4. 尝试切换 LLM 提供商

## 🔍 故障排查命令

### 检查安装状态
```zsh
# 检查 uvx 位置和版本
which uvx
uvx --version

# 检查直接路径是否存在
ls -la /Users/<USER>/.local/bin/mcp-server-browser-use

# 检查符号链接目标
readlink /Users/<USER>/.local/bin/mcp-server-browser-use
```

### 测试 MCP 服务器
```zsh
# 测试直接路径调用
/Users/<USER>/.local/bin/mcp-server-browser-use --help

# 测试 uvx 调用（会比较慢）
uvx mcp-server-browser-use@latest --help
```

### 检查环境
```zsh
# 查看 PATH 环境变量
echo $PATH

# 检查当前 shell
echo $SHELL

# 检查 Playwright 浏览器
uvx --from mcp-server-browser-use@latest python -m playwright install --help
```

## 📊 配置方式详细对比

| 配置方式 | 启动时间 | 稳定性 | 维护性 | 环境依赖 | 推荐度 | 说明 |
|----------|----------|--------|--------|----------|--------|------|
| 直接路径 | <0.1秒 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | 低 | ✅ 推荐 | 本机验证成功 |
| uvx 相对路径 | ~1.4秒 | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | 高 | ❌ 不推荐 | 本机验证失败 |
| uvx 完整路径 | ~1.4秒 | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | 中 | 🔄 备选 | 理论可行 |

### 对比说明
- **启动时间**: 直接路径立即启动，uvx 需要依赖解析
- **稳定性**: 固定环境 > 动态环境
- **维护性**: uvx 自动更新 > 手动管理版本
- **环境依赖**: PATH、权限、网络等外部因素影响

## 🎯 最佳实践

### 1. 配置原则
- **优先使用直接路径**：启动快、稳定性好
- **备份 API 密钥**：准备多个 LLM 提供商的密钥
- **定期更新**：保持 mcp-server-browser-use 版本最新

### 2. 环境变量说明
```zsh
# 必需环境变量
MCP_LLM_PROVIDER=google                    # LLM 提供商
MCP_LLM_MODEL_NAME=gemini-2.0-flash-exp    # 模型名称
MCP_LLM_GOOGLE_API_KEY=your-api-key        # API 密钥

# 可选环境变量
MCP_BROWSER_HEADLESS=true                  # 无头模式
MCP_BROWSER_WINDOW_WIDTH=1440              # 浏览器宽度
MCP_BROWSER_WINDOW_HEIGHT=1080             # 浏览器高度
MCP_AGENT_TOOL_USE_VISION=true             # 启用视觉功能
```

### 3. 故障排查流程
1. **检查配置**：确认路径和环境变量正确
2. **验证安装**：确认 mcp-server-browser-use 已安装
3. **测试 API**：验证 LLM API 密钥有效
4. **查看日志**：检查 Augment Code 错误日志
5. **重启服务**：重启 Augment Code 应用

## 📝 更新记录

- **2025-01-17 v1.0**: 初始版本，记录 uvx vs 直接路径配置问题
- **2025-01-17 v1.1**: 重新组织文档结构
  - 新增本机环境详细信息
  - 深度分析配置失败的根本原因
  - 优化推荐配置和故障排查流程
  - 完善配置对比表
- **2025-01-17 v1.2**: 完善配置选择策略
  - 新增配置方式选择判断标准
  - 添加 uvx vs 直接路径的适用场景分析
  - 提供混合配置示例
  - 修正终端环境描述（bash → zsh）
- **待更新**: 根据新问题持续更新

## 🔗 相关链接

- [mcp-server-browser-use GitHub](https://github.com/Saik0s/mcp-browser-use)
- [browser-use GitHub](https://github.com/browser-use/browser-use)
- [Google AI Studio](https://aistudio.google.com/app/apikey)
- [MCP 官方文档](https://modelcontextprotocol.io/)

---

**提示**：遇到新问题时，请及时更新此手册，为后续排查提供参考。
